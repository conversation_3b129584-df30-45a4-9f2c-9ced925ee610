/* Reset default browser margins and padding */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

/* Map container styles */
#map {
  height: 100vh;
  width: 100vw;
}

/* Control panel container */
#container {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: white;
  padding: 15px;
  border: 1px solid #ccc;
  z-index: 1000;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: max-height 0.3s ease; /* Add transition for smooth minimizing */
}

/* Control panel title */
#container h3 {
  margin: 0 0 10px 0;
  color: #333;
}

/* Statistics section */
#stats {
  margin-bottom: 15px;
}

/* Statistics rows */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stats-row:last-child {
  margin-bottom: 10px;
}

/* Visited count styling */
.visited-text {
  color: #28a745;
}

.visited-count {
  color: #28a745;
}

/* Not visited count styling */
.not-visited-text {
  color: #6c757d;
}

.not-visited-count {
  color: #6c757d;
}

/* Progress and stats container */
.progress-stats-container {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 15px;
}

/* Progress section */
.progress-section {
  margin-bottom: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
}

.progress-bar-container {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  margin-top: 5px;
}

#progress-bar {
  background: #28a745;
  height: 100%;
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
}

/* Compact statistics */
.stats-compact {
  margin-top: 8px;
}

.stats-row-compact {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  gap: 8px;
  flex-wrap: wrap;
}

.stats-row-compact > span {
  white-space: nowrap;
}

/* Button container */
.button-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 15px; /* Add margin to separate from slider */
}

/* Button styles */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn:hover {
  opacity: 0.9;
}

.btn-warning:hover {
  background: #e0a800;
}

/* File input styling */
#import-file {
  display: none;
}

/* Popup content styling */
.popup-content {
  font-family: Arial, sans-serif;
}

.popup-content h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.checkbox-container {
  margin-bottom: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-input, .checkbox-input-campaign {
  transform: scale(1.5);
}

.checkbox-input-campaign {
  accent-color: #dc3545;
}

.status-text-visited {
  color: #28a745;
}

.status-text-not-visited {
  color: #6c757d;
}

.popup-info {
  margin: 5px 0;
  color: #666;
}

.popup-info i {
  margin-right: 8px;
  width: 16px;
}

/* Campaign input section */
.campaign-input-section {
  margin-bottom: 15px;
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.campaign-input {
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  flex: 1;
}

.campaign-input:focus {
  outline: none;
  border-color: #007bff;
}

/* Campaign info section */
.campaign-info {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  max-height: 165px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #6c757d #f8f9fa; /* Firefox */
}

/* Webkit scrollbar styling */
.campaign-info::-webkit-scrollbar {
  width: 6px;
}

.campaign-info::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.campaign-info::-webkit-scrollbar-thumb {
  background: #6c757d;
  border-radius: 3px;
}

.campaign-info::-webkit-scrollbar-thumb:hover {
  background: #495057;
}

.campaigns-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.campaign-item {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px;
  background-color: white;
}

.campaign-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.campaign-info-text {
  flex: 1;
  min-width: 0;
}

.campaign-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
  max-width: 250px;
}

.campaign-id {
  color: #6c757d;
  font-size: 10px;
}

.campaign-clear-btn {
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
  white-space: nowrap;
}

.campaign-clear-btn:hover {
  opacity: 0.8;
}



/* Legacy styles for backward compatibility */
.campaign-info-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.campaign-info-content i {
  color: #dc3545;
}

.red-marker-indicator {
  background-color: #dc3545;
  color: white;
  padding: 6px 7px;
  border-radius: 3px;
  font-size: 12px;
  margin-left: auto;
}

/* City filter section */
.city-filter-section {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.city-filter-section label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.city-filter-select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  background-color: white;
  cursor: pointer;
}

.city-filter-select:focus {
  outline: none;
  border-color: #007bff;
}

/* Grey red markers toggle */
.grey-markers-toggle {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.grey-markers-toggle .checkbox-label {
  font-size: 12px;
  margin: 0;
}

/* Refresh button */
.btn-refresh {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.btn-refresh:hover {
  background-color: #138496;
}

.btn-refresh i {
  font-size: 10px;
}

/* Legend button */
.btn-legend {
  background-color: #6f42c1;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.btn-legend:hover {
  background-color: #5a2d91;
}

.btn-legend i {
  font-size: 10px;
}

/* Marker legend */
.marker-legend {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.legend-header {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 8px;
  color: #495057;
}

.legend-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
  margin-bottom: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.legend-marker {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-colors {
  border-top: 1px solid #dee2e6;
  padding-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.legend-color-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.legend-color-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #000;
}

/* Header with minimize button */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.header h3 {
  margin: 0;
  color: #333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.minimize-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  margin-left: 8px;
}

.minimize-btn:hover {
  background: #5a6268;
}

#tracker-content {
  overflow-y: auto;
  max-height: 80vh;
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
}

/* This is the existing class to hide the content */
.container-minimized #tracker-content {
  display: none;
}

/* Clustering toggle */
.clustering-toggle {
  margin: 10px 0;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.clustering-toggle .checkbox-label {
  font-size: 12px;
  margin: 0;
}

/* Legacy slider styles (kept for backward compatibility) */
.slider-container {
  margin: 10px 0;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
}

.slider-container input[type="range"] {
  width: 100%;
}

.leaflet-touch .leaflet-bar a {
	display: none;
}

/* className: 'my-div-icon' */
.my-div-icon {
  background: none;
  border: none;
  box-shadow: none;
  cursor: pointer;
  display: block;
  margin: 0;
  padding: 0;
}


/* --- MOBILE RESPONSIVENESS --- */
/* These styles will apply only on screens 768px wide or smaller */
@media (max-width: 768px) {
  #container {
    top: 10px; /* Position from the top */
    bottom: auto; /* Remove the bottom constraint */
    left: 10px;
    right: 10px;
    width: auto; /* Let left/right define the width */
    margin: 0 auto; /* Center the panel */
    max-width: 400px; /* Stop it from getting too wide on tablets */

    max-height: 100vh; /* Ensure it's never taller than 85% of the screen */
    overflow-y: auto; /* Make the panel itself scrollable if content overflows */
  }

  .campaigns-container {
    max-height: 150px;
  }

  /* When minimized on mobile, remove scrolling and ensure it's small */
  #container.container-minimized {
    overflow-y: hidden;
    max-height: 60px; /* A fixed small height for the header */
  }

  /* Campaign list mobile improvements */
  .campaign-info {
    max-height: 200px; /* Smaller height on mobile */
  }

  .stats-row-compact {
    display: flex;
    flex-wrap: nowrap;
    gap: 1em;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
  }

  .stats-row-compact > span {
    flex-shrink: 0;
  }

  .grey-markers-toggle {
    flex-direction: row;
    align-items: center;
    gap: 10px;
    flex-wrap: nowrap;
    white-space: nowrap;
    overflow-x: auto;
  }
}